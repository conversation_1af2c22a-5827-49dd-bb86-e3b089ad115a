// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "../governance/interfaces/IStoragePool.sol";

/**
 * @title StoragePoolGetters
 * @dev Library containing getter functions for StoragePool to reduce contract size
 */
library StoragePoolGetters {
    
    function getPools(uint32[] storage poolIds) external view returns (uint32[] memory) {
        return poolIds;
    }
    
    function getPoolMemberAt(
        mapping(uint32 => IStoragePool.Pool) storage pools,
        uint32 p,
        uint256 i
    ) external view returns (address) {
        return pools[p].memberList[i];
    }
    
    function getMemberPeerIds(
        mapping(uint32 => IStoragePool.Pool) storage pools,
        uint32 p,
        address a
    ) external view returns (string[] memory) {
        return pools[p].memberPeerIds[a];
    }
    
    function getLockedTokensForPeer(
        mapping(uint32 => IStoragePool.Pool) storage pools,
        uint32 p,
        string calldata peerId
    ) external view returns (uint256) {
        return pools[p].lockedTokens[peerId];
    }
    
    function isPeerInPool(
        mapping(uint32 => IStoragePool.Pool) storage pools,
        uint32 p,
        string calldata peerId
    ) external view returns (bool) {
        return pools[p].peerIdToMember[peerId] != address(0);
    }
    
    function isJoinRequestPending(
        mapping(uint32 => mapping(string => IStoragePool.JoinRequest)) storage joinRequests,
        uint32 p,
        string calldata peerId
    ) external view returns (bool) {
        return joinRequests[p][peerId].account != address(0) && joinRequests[p][peerId].status == 1;
    }
    
    function getPendingJoinRequests(
        mapping(uint32 => string[]) storage joinRequestKeys,
        mapping(uint32 => mapping(string => IStoragePool.JoinRequest)) storage joinRequests,
        uint32 p,
        uint256 o,
        uint256 l
    ) external view returns (
        string[] memory peerIds,
        address[] memory accounts,
        uint128[] memory approvals,
        uint128[] memory rejections,
        uint32[] memory timestamps,
        uint256 totalCount
    ) {
        string[] storage k = joinRequestKeys[p];
        totalCount = k.length;
        if (o >= totalCount) return (new string[](0), new address[](0), new uint128[](0), new uint128[](0), new uint32[](0), totalCount);
        
        uint256 e = o + l > totalCount ? totalCount : o + l;
        uint256 len = e - o;
        peerIds = new string[](len);
        accounts = new address[](len);
        approvals = new uint128[](len);
        rejections = new uint128[](len);
        timestamps = new uint32[](len);
        
        for (uint256 i = 0; i < len; i++) {
            IStoragePool.JoinRequest storage r = joinRequests[p][k[o + i]];
            peerIds[i] = k[o + i];
            accounts[i] = r.account;
            approvals[i] = r.approvals;
            rejections[i] = r.rejections;
            timestamps[i] = r.timestamp;
        }
    }
    
    function isPeerIdMemberOfPool(
        mapping(uint32 => IStoragePool.Pool) storage pools,
        uint32 p,
        string calldata peerId
    ) external view returns (bool isMember, address memberAddress) {
        memberAddress = pools[p].peerIdToMember[peerId];
        isMember = memberAddress != address(0);
    }
    
    function getTotalMembers(
        uint32[] storage poolIds,
        mapping(uint32 => IStoragePool.Pool) storage pools
    ) external view returns (uint256 total) {
        for (uint256 i = 0; i < poolIds.length; i++) {
            total += pools[poolIds[i]].memberCount;
        }
    }
    
    function isMemberOfAnyPool(
        uint32[] storage poolIds,
        mapping(uint32 => IStoragePool.Pool) storage pools,
        address a
    ) external view returns (bool) {
        for (uint256 i = 0; i < poolIds.length; i++) {
            if (pools[poolIds[i]].memberPeerIds[a].length > 0) return true;
        }
        return false;
    }
}
